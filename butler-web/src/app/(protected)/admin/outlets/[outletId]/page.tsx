"use client";

import { useEffect, useRef, useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import OutletQrCode from "@/components/custom/outlet/OutletQrCode";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Phone,
  Calendar,
  Store,
  ChevronLeft,
  IndianRupee,
  PlusCircle,
  Edit,
  Settings,
  Utensils,
  Eye,
  EyeOff,
  Star,
  MoreVertical,
} from "lucide-react";
import { getSingleOutlet, updateDish, toggleDishFeaturedStatus } from "@/server/admin";
import { Outlet, Dish } from "@/app/type";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { Icon } from "@iconify/react/dist/iconify.js";
import CreateDishesDrawer from "@/components/custom/dishes/CreateDishesDrawer";
import DishOutletManagement from "@/components/custom/dishes/DishOutletManagement";
import DishAvailabilitySettings from "@/components/custom/dishes/DishAvailabilitySettings";
import DishIngredientsDialog from "@/components/custom/dishes/DishIngredientsDialog";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const quotes: string[] = [
  `Welcome to {outlet.name}`,
  `Point camera here. Ask AI about our food.`,
  `QR code → Chat → Perfect meal suggestions.`,
  `Not sure what to eat? AI knows our menu better than anyone.`,
];

const OutletViewPage = () => {
  const router = useRouter();
  const params = useSearchParams();
  const isViewPage = params ? params.get("view") === "true" : false;
  const { outletId } = useParams() || {};
  const [outlet, setOutlet] = useState<Outlet | null>(null);
  const [loading, setLoading] = useState(true);
  const qrCodeRef = useRef(null);
  const [selectedQuote, setSelectedQuote] = useState("");

  // Dish management states
  const [dishToEdit, setDishToEdit] = useState<Dish | false>(false);
  const [dishForOutletManagement, setDishForOutletManagement] = useState<Dish | null>(null);
  const [dishForAvailability, setDishForAvailability] = useState<Dish | null>(null);
  const [dishForIngredients, setDishForIngredients] = useState<Dish | null>(null);
  const [showCreateDish, setShowCreateDish] = useState(false);

  useEffect(() => {
    // Mock API call - replace with actual API call
    const fetchOutletData = async () => {
      try {
        const res = await getSingleOutlet(outletId as string);

        setOutlet(res.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching outlet data:", error);
        setLoading(false);
      }
    };

    if (outletId) {
      fetchOutletData();
    }
  }, [outletId]);

  // Dish management functions
  const refreshOutletData = async () => {
    try {
      const res = await getSingleOutlet(outletId as string);
      setOutlet(res.data);
    } catch (error) {
      console.error("Error refreshing outlet data:", error);
    }
  };

  const handleToggleDishAvailability = async (dish: Dish) => {
    try {
      const updatedDish = { ...dish, isAvailable: !dish.isAvailable };
      const response = await updateDish(updatedDish);

      if (response.success) {
        toast.success(`Dish ${dish.isAvailable ? 'disabled' : 'enabled'} successfully`);
        refreshOutletData();
      } else {
        toast.error("Failed to update dish availability");
      }
    } catch (error) {
      console.error("Error updating dish availability:", error);
      toast.error("Failed to update dish availability");
    }
  };

  const handleToggleFeatured = async (dish: Dish) => {
    try {
      const response = await toggleDishFeaturedStatus(dish._id);

      if (response.success) {
        toast.success(`Dish ${dish.isFeatured ? 'removed from' : 'added to'} featured`);
        refreshOutletData();
      } else {
        toast.error("Failed to update featured status");
      }
    } catch (error) {
      console.error("Error updating featured status:", error);
      toast.error("Failed to update featured status");
    }
  };

  useEffect(() => {
    setSelectedQuote(
      quotes[Math.floor(Math.random() * quotes.length)].replace(
        "{outlet.name}",
        outlet?.name || ""
      )
    );
    const intervalId = setInterval(() => {
      setSelectedQuote(
        quotes[Math.floor(Math.random() * quotes.length)].replace(
          "{outlet.name}",
          outlet?.name || ""
        )
      );
    }, 10000);
    return () => clearInterval(intervalId);
  }, [outlet]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg font-medium">Loading outlet information...</div>
      </div>
    );
  }

  if (!outlet) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg font-medium text-red-500">Outlet not found</div>
      </div>
    );
  }

  if (isViewPage) {
    return (
      <div className="absolute inset-0 bg-white min-h-screen flex flex-col items-center justify-center p-6 z-50">
        {/* Download Button */}
        <div className="absolute top-10 right-10">
          <div
            className="flex justify-center gap-2 cursor-pointer"
            // onClick={handleDownload}
            onClick={() => window.print()}
          >
            <Icon icon="line-md:download-loop" width="30" height="30" />
          </div>
        </div>

        {/* Page Content */}
        <div ref={qrCodeRef}>
          {/* Absolute Logo */}
          <Image
            src="/ai_image.png"
            alt="Outlet Logo"
            width={200}
            height={200}
            className="absolute bottom-20 right-10"
          />

          <div className="max-w-md w-full mx-auto text-center space-y-8">
            {/* Header */}
            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-gray-900 gradient-flow">
                {selectedQuote}
              </h1>
              <p className="text-lg text-gray-600">
                Scan the QR code to chat with your personal AI butler
              </p>
            </div>

            {/* QR Code Section */}
            <div className="bg-white p-8 rounded-2xl shadow-lg border-2 border-blue-100 flex flex-col items-center">
              <div className="mb-6">
                <OutletQrCode outletId={String(outletId)} onlyQr />
              </div>
              <p className="text-sm text-gray-500">
                Point your camera at the QR code to start ordering
              </p>
            </div>

            {/* Features Section */}
            <div className="grid grid-cols-2 gap-4 text-center mt-8">
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-semibold text-blue-900">Easy Ordering</h3>
                <p className="text-sm text-blue-700">
                  Order directly from your phone
                </p>
              </div>
              <div className="p-4 rounded-lg bg-blue-50">
                <h3 className="font-semibold text-blue-900">AI Assistant</h3>
                <p className="text-sm text-blue-700">
                  Get personalized recommendations
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="text-sm text-gray-500 flex justify-center items-center gap-1.5">
              Powered by{" "}
              <span className="font-bold text-blue-700">Butler AI</span>
              <Image
                src={"/logos/butler.png"}
                alt={"Butler"}
                width={20}
                height={20}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-2 px-1 md:px-4">
      <Button className="mb-3" onClick={() => router.back()}>
        <ChevronLeft /> Back
      </Button>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="shadow-md">
            <CardHeader className="border-b pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="md:text-2xl text-md font-bold">
                    {outlet.name}
                  </CardTitle>
                  <p className="text-gray-500 mt-1">
                    {typeof outlet?.foodChain === "string"
                      ? outlet?.foodChain
                      : outlet?.foodChain?.name}
                  </p>
                </div>
                <Badge
                  variant={outlet.isCloudKitchen ? "default" : "outline"}
                  className={outlet.isCloudKitchen ? "bg-blue-500" : ""}
                >
                  {outlet.isCloudKitchen
                    ? "Cloud Kitchen"
                    : "Physical Location"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="md:pt-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Address</h3>
                    <p className="text-gray-600">{outlet.address}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Contact</h3>
                    <p className="text-gray-600">{outlet.contact}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Store className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Food Chain</h3>
                    <p className="text-gray-600">
                      {typeof outlet?.foodChain === "string"
                        ? outlet?.foodChain
                        : outlet?.foodChain?.name}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold">Created</h3>
                    <p className="text-gray-600">
                      {formatDate(outlet.createdAt)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Last updated: {formatDate(outlet.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="shadow-md">
            <CardHeader>
              <CardTitle className="text-lg font-medium">
                Outlet QR Code
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <div className="mb-4 p-4 bg-white rounded-lg shadow-sm">
                <OutletQrCode outletId={String(outletId)} />
              </div>
              <p className="text-sm text-gray-500 text-center">
                Scan this QR code to talk with Outlet&apos;s Butler
              </p>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle>Dishes Management</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8"
                  onClick={() => setShowCreateDish(true)}
                >
                  <PlusCircle className="h-4 w-4 mr-1" />
                  Add New Dish
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8"
                  onClick={() =>
                    router.push(`/admin/dishes?outletId=${outletId}`)
                  }
                >
                  Manage All Dishes
                </Button>
              </div>
            </CardHeader>
            <CardContent className="flex h-[400px] overflow-y-scroll flex-col">
              {outlet.dishes && outlet.dishes.length > 0 ? (
                outlet.dishes.map((dish) => (
                  <div
                    className="m-2 p-3 border rounded-lg flex justify-between items-center gap-3 w-full h-fit hover:shadow-sm transition-shadow"
                    key={dish._id}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{dish.name}</span>
                          {dish.isFeatured && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                          {!dish.isAvailable && (
                            <Badge variant="secondary" className="text-xs">
                              Unavailable
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <IndianRupee className="h-3 w-3" />
                          <span>{dish.price}</span>
                          {dish.category && (
                            <>
                              <span>•</span>
                              <span>{typeof dish.category === 'string' ? dish.category : dish.category.name}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <span className="text-xs text-gray-500">Available</span>
                        <Switch
                          checked={dish.isAvailable}
                          onCheckedChange={() => handleToggleDishAvailability(dish)}
                        />
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setDishToEdit(dish)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Dish
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleFeatured(dish)}>
                            <Star className="h-4 w-4 mr-2" />
                            {dish.isFeatured ? 'Remove from Featured' : 'Mark as Featured'}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setDishForAvailability(dish)}>
                            <Settings className="h-4 w-4 mr-2" />
                            Availability Settings
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setDishForOutletManagement(dish)}>
                            <Store className="h-4 w-4 mr-2" />
                            Manage Outlets
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setDishForIngredients(dish)}>
                            <Utensils className="h-4 w-4 mr-2" />
                            Manage Ingredients
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <p>No dishes added to this outlet yet</p>
                  <Button
                    variant="link"
                    onClick={() => setShowCreateDish(true)}
                  >
                    Add your first dish
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Dish Management Dialogs */}
      {(showCreateDish || dishToEdit) && (
        <CreateDishesDrawer
          onSuccess={() => {
            refreshOutletData();
            setDishToEdit(false);
            setShowCreateDish(false);
          }}
          isUpdate={dishToEdit ? true : false}
          dishToEdit={dishToEdit !== false ? dishToEdit : undefined}
          onClose={() => {
            setDishToEdit(false);
            setShowCreateDish(false);
          }}
          preselectedOutletId={outletId as string}
        />
      )}

      {dishForOutletManagement && (
        <DishOutletManagement
          dish={dishForOutletManagement}
          open={!!dishForOutletManagement}
          onOpenChange={(open) => !open && setDishForOutletManagement(null)}
          onSuccess={() => {
            refreshOutletData();
            setDishForOutletManagement(null);
          }}
        />
      )}

      {dishForAvailability && (
        <DishAvailabilitySettings
          dish={dishForAvailability}
          open={!!dishForAvailability}
          onOpenChange={(open) => !open && setDishForAvailability(null)}
          onSuccess={() => {
            refreshOutletData();
            setDishForAvailability(null);
          }}
        />
      )}

      {dishForIngredients && (
        <DishIngredientsDialog
          dish={dishForIngredients}
          open={!!dishForIngredients}
          onClose={(refresh) => {
            if (refresh) refreshOutletData();
            setDishForIngredients(null);
          }}
        />
      )}
    </div>
  );
};

export default OutletViewPage;
